HOST=127.0.0.1
PORT=8000

#key and url
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1/
DEEP_SEEK_BASE_URL=https://api.deepseek.com/v1
CHAT_ANY_WHERE_BASE_URL=https://api.chatanywhere.tech/v1
xxxxxxx_BASE_URL=https://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# 定义你要用的api的key(需要去对应网站申请哦)
DEEP_SEEK_KEY=
CHAT_ANY_WHERE_KEY=
SILICONFLOW_KEY=
xxxxxxx_KEY=

# 定义日志相关配置

# 精简控制台输出格式
SIMPLE_OUTPUT=true

# 自定义日志的默认控制台输出日志级别
CONSOLE_LOG_LEVEL=INFO

# 自定义日志的默认文件输出日志级别
FILE_LOG_LEVEL=DEBUG

# 原生日志的控制台输出日志级别
DEFAULT_CONSOLE_LOG_LEVEL=SUCCESS

# 原生日志的默认文件输出日志级别
DEFAULT_FILE_LOG_LEVEL=DEBUG
