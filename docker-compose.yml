services:
  adapters:
    container_name: maim-bot-adapters
    image: unclas/maimbot-adapter:latest
    # image: infinitycat/maimbot-adapter:latest
    environment:
      - TZ=Asia/Shanghai
#    ports:
#      - "8095:8095"
    volumes:
      - ./docker-config/adapters/config.toml:/adapters/config.toml
    restart: always
    depends_on:
      - mongodb
    networks:
      - maim_bot
  core:
    container_name: maim-bot-core
    image: sengokucola/maibot:latest
    # image: infinitycat/maibot:latest
    # dev
    # image: sengokucola/maibot:dev
    # image: infinitycat/maibot:dev
    environment:
      - TZ=Asia/Shanghai
#      - EULA_AGREE=bda99dca873f5d8044e9987eac417e01  # 同意EULA
#      - PRIVACY_AGREE=42dddb3cbe2b784b45a2781407b298a1 # 同意EULA
#    ports:
#      - "8000:8000"
    volumes:
      - ./docker-config/mmc/.env:/MaiMBot/.env # 持久化env配置文件
      - ./docker-config/mmc:/MaiMBot/config # 持久化bot配置文件
      - ./data/MaiMBot:/MaiMBot/data # NapCat 和 NoneBot 共享此卷，否则发送图片会有问题
    restart: always
    depends_on:
      - mongodb
    networks:
      - maim_bot
  mongodb:
    container_name: maim-bot-mongo
    environment:
      - TZ=Asia/Shanghai
#      - MONGO_INITDB_ROOT_USERNAME=your_username # 此处配置mongo用户
#      - MONGO_INITDB_ROOT_PASSWORD=your_password # 此处配置mongo密码
#    ports:
#      - "27017:27017"
    restart: always
    volumes:
      - mongodb:/data/db #  持久化mongodb数据
      - mongodbCONFIG:/data/configdb # 持久化mongodb配置文件
    image: mongo:latest
    networks:
      - maim_bot
  napcat:
    environment:
      - NAPCAT_UID=1000
      - NAPCAT_GID=1000
      - TZ=Asia/Shanghai
    ports:
      - "6099:6099"
    volumes:
      - ./docker-config/napcat:/app/napcat/config # 持久化napcat配置文件
      - ./data/qq:/app/.config/QQ # 持久化QQ本体并同步qq表情和图片到adapters
      - ./data/MaiMBot:/MaiMBot/data # NapCat 和 NoneBot 共享此卷，否则发送图片会有问题
    container_name: maim-bot-napcat
    restart: always
    image: mlikiowa/napcat-docker:latest
    networks:
      - maim_bot
networks:
  maim_bot:
    driver: bridge
volumes:
  mongodb:
  mongodbCONFIG: