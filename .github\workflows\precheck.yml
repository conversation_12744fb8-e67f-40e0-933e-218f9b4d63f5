# .github/workflows/precheck.yml
name: PR Precheck
on: [pull_request]

jobs:
  conflict-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Check Conflicts
        run: |
          git fetch origin main
          if git diff --name-only --diff-filter=U origin/main...HEAD | grep .; then
            echo "CONFLICT=true" >> $GITHUB_ENV
          fi
  labeler:
    runs-on: ubuntu-latest
    needs: conflict-check
    steps:
      - uses: actions/github-script@v6
        if: env.CONFLICT == 'true'
        with:
          script: |
            github.rest.issues.addLabels({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              labels: ['🚫冲突需处理']
            })
